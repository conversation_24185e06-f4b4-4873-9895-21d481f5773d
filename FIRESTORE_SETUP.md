# إعداد Firestore Database - دليل سريع

## 🔥 خطوات إعداد قاعدة البيانات

### 1. **الذهاب إلى Firebase Console**
- افتح [Firebase Console](https://console.firebase.google.com/)
- اختر مشروعك: `factory-manager-31ba1`

### 2. **إنشاء Firestore Database**

#### أ. الوصول إلى Firestore
- من القائمة الجانبية، انقر على **"Firestore Database"**
- انقر على **"إنشاء قاعدة بيانات"** (Create database)

#### ب. اختيار وضع الأمان
ستظهر لك خيارين:

**الخيار الأول: وضع الإنتاج (Production mode)**
- اختر هذا الخيار
- انقر **"التالي"** (Next)

#### ج. اختيار الموقع
- اختر أقرب موقع لك (مثل: `europe-west1` أو `us-central1`)
- انقر **"تم"** (Done)

### 3. **إعداد قواعد الأمان**

بعد إنشاء قاعدة البيانات:

#### أ. الذهاب إلى قواعد الأمان
- انقر على تبويب **"Rules"**

#### ب. تحديث القواعد
استبدل القواعد الموجودة بهذا الكود:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح للمستخدمين المصادق عليهم فقط
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### ج. نشر القواعد
- انقر **"نشر"** (Publish)

### 4. **إنشاء المجموعات الأساسية (اختياري)**

يمكنك إنشاء المجموعات مسبقاً:

#### أ. إنشاء مجموعة المنتجات
- انقر **"بدء مجموعة"** (Start collection)
- اسم المجموعة: `products`
- انقر **"التالي"**
- أضف وثيقة تجريبية:
  - معرف الوثيقة: `sample`
  - الحقول:
    ```
    name: "منتج تجريبي"
    category: "كريمات الوجه"
    quantity: 0
    cost: 0
    price: 0
    materials: []
    ```
- انقر **"حفظ"**

#### ب. إنشاء مجموعة المواد الخام
- كرر نفس الخطوات لمجموعة `materials`

#### ج. إنشاء مجموعة الإعدادات
- كرر نفس الخطوات لمجموعة `settings`

### 5. **اختبار الاتصال**

#### أ. تشغيل التطبيق
```bash
npm run dev
```

#### ب. تسجيل الدخول
- اذهب إلى: http://localhost:8080
- سجل دخول بحسابك

#### ج. اختبار إضافة منتج
- اذهب إلى صفحة "المنتجات"
- انقر "إضافة منتج جديد"
- املأ البيانات واحفظ

#### د. التحقق من قاعدة البيانات
- ارجع إلى Firebase Console
- تحقق من ظهور البيانات في مجموعة `products`

## ✅ **علامات النجاح**

إذا تم كل شيء بنجاح، ستلاحظ:

1. ✅ **قاعدة البيانات منشأة** في Firebase Console
2. ✅ **قواعد الأمان محدثة** ومنشورة
3. ✅ **التطبيق يعمل** بدون أخطاء
4. ✅ **إضافة المنتجات تعمل** والبيانات تظهر في Firebase
5. ✅ **التحديث الفوري** - التغييرات تظهر مباشرة

## 🔧 **استكشاف الأخطاء**

### خطأ في الصلاحيات
```
FirebaseError: Missing or insufficient permissions
```
**الحل**: تأكد من تحديث قواعد الأمان كما هو موضح أعلاه

### خطأ في الاتصال
```
FirebaseError: Failed to get document
```
**الحل**: 
- تأكد من الاتصال بالإنترنت
- تأكد من صحة تكوين Firebase

### البيانات لا تظهر
**الحل**:
- تأكد من تسجيل الدخول
- تحقق من console المتصفح للأخطاء
- تأكد من إنشاء قاعدة البيانات

## 🎉 **تهانينا!**

الآن لديك:
- 🔥 **Firebase Authentication** للأمان
- 🗄️ **Firestore Database** لحفظ البيانات
- ⚡ **تحديث فوري** للبيانات
- 🌐 **نسخ احتياطي تلقائي** في السحابة

التطبيق جاهز للاستخدام الكامل! 🚀
