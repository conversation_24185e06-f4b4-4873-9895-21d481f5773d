# إعداد Firebase للمصادقة - دليل شامل

## 🚀 خطوات إعداد Firebase

### 1. إنشاء مشروع Firebase

1. **اذهب إلى Firebase Console**
   - افتح [Firebase Console](https://console.firebase.google.com/)
   - سجل دخول بحساب Google الخاص بك

2. **إنشاء مشروع جديد**
   - انقر على "إضافة مشروع" (Add project)
   - أدخل اسم المشروع: `cream-factory-manager` (أو أي اسم تفضله)
   - اختر البلد/المنطقة: مصر
   - اقبل شروط الخدمة
   - انقر على "إنشاء مشروع"
   - انتظر حتى ينتهي الإعداد (قد يستغرق دقيقة)

### 2. إعد<PERSON> Authentication (المصادقة)

1. **تفعيل Authentication**
   - في لوحة تحكم Firebase، انقر على "Authentication" من القائمة الجانبية
   - انقر على "البدء" (Get started)

2. **إعداد طريقة تسجيل الدخول**
   - اذهب إلى تبويب "Sign-in method"
   - ابحث عن "Email/Password"
   - انقر على "Email/Password"
   - فعّل الخيار الأول "Email/Password"
   - **لا تفعل** "Email link (passwordless sign-in)"
   - انقر على "حفظ" (Save)

### 3. إضافة تطبيق ويب

1. **إضافة التطبيق**
   - في نظرة عامة على المشروع، انقر على أيقونة الويب `</>`
   - أدخل اسم التطبيق: `cream-factory-web`
   - **لا تحتاج** لتفعيل Firebase Hosting الآن
   - انقر على "تسجيل التطبيق" (Register app)

2. **نسخ التكوين**
   - ستظهر لك صفحة بها كود JavaScript
   - انسخ فقط الجزء الخاص بـ `firebaseConfig`
   - سيبدو مثل هذا:
   ```javascript
   const firebaseConfig = {
     apiKey: "AIzaSyC...",
     authDomain: "your-project.firebaseapp.com",
     projectId: "your-project-id",
     storageBucket: "your-project.appspot.com",
     messagingSenderId: "123456789",
     appId: "1:123456789:web:abcdef"
   };
   ```

### 4. تحديث ملف التكوين

افتح ملف `src/lib/firebase.ts` وقم بتحديث `firebaseConfig` بالقيم الخاصة بمشروعك:

```typescript
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

### 5. إنشاء حساب المدير

1. في Firebase Console، اذهب إلى "Authentication"
2. انقر على تبويب "Users"
3. انقر على "إضافة مستخدم" (Add user)
4. أدخل البريد الإلكتروني وكلمة المرور للمدير
5. انقر على "إضافة مستخدم"

### 6. قواعد الأمان (اختياري)

يمكنك إضافة قواعد أمان إضافية في Firebase Console:

1. اذهب إلى "Authentication" > "Settings"
2. في تبويب "Authorized domains"، تأكد من إضافة النطاق الخاص بك

## استخدام النظام

بعد إكمال الإعداد:

1. شغّل التطبيق: `npm run dev`
2. ستظهر صفحة تسجيل الدخول
3. استخدم البريد الإلكتروني وكلمة المرور التي أنشأتها في Firebase
4. بعد تسجيل الدخول بنجاح، ستتمكن من الوصول لجميع صفحات النظام

## ملاحظات مهمة

- فقط المستخدمين المضافين في Firebase Console يمكنهم تسجيل الدخول
- لا يوجد تسجيل مستخدمين جدد من التطبيق - يجب إضافتهم من Firebase Console
- تأكد من الحفاظ على أمان بيانات Firebase الخاصة بك
- لا تشارك مفاتيح API في أماكن عامة

## استكشاف الأخطاء

إذا واجهت مشاكل:

1. تأكد من صحة تكوين Firebase
2. تأكد من تفعيل Email/Password authentication
3. تأكد من وجود المستخدم في Firebase Console
4. تحقق من console المتصفح للأخطاء
