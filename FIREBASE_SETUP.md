# إعداد Firebase للمصادقة

## خطوات إعداد Firebase

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إضافة مشروع" (Add project)
3. أ<PERSON><PERSON><PERSON> اسم المشروع: `cream-factory-manager`
4. اختر إعدادات المشروع حسب الحاجة
5. انقر على "إنشاء مشروع"

### 2. إعداد Authentication

1. في لوحة تحكم Firebase، اذهب إلى "Authentication"
2. انقر على "البدء" (Get started)
3. اذهب إلى تبويب "Sign-in method"
4. فعّل "Email/Password" كطريقة تسجيل دخول
5. احفظ الإعدادات

### 3. إضا<PERSON>ة تطبيق ويب

1. في نظرة عامة على المشروع، انقر على أيقونة الويب `</>`
2. أ<PERSON><PERSON><PERSON> اسم التطبيق: `cream-factory-web`
3. انقر على "تسجيل التطبيق"
4. انسخ تكوين Firebase (firebaseConfig)

### 4. تحديث ملف التكوين

افتح ملف `src/lib/firebase.ts` وقم بتحديث `firebaseConfig` بالقيم الخاصة بمشروعك:

```typescript
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

### 5. إنشاء حساب المدير

1. في Firebase Console، اذهب إلى "Authentication"
2. انقر على تبويب "Users"
3. انقر على "إضافة مستخدم" (Add user)
4. أدخل البريد الإلكتروني وكلمة المرور للمدير
5. انقر على "إضافة مستخدم"

### 6. قواعد الأمان (اختياري)

يمكنك إضافة قواعد أمان إضافية في Firebase Console:

1. اذهب إلى "Authentication" > "Settings"
2. في تبويب "Authorized domains"، تأكد من إضافة النطاق الخاص بك

## استخدام النظام

بعد إكمال الإعداد:

1. شغّل التطبيق: `npm run dev`
2. ستظهر صفحة تسجيل الدخول
3. استخدم البريد الإلكتروني وكلمة المرور التي أنشأتها في Firebase
4. بعد تسجيل الدخول بنجاح، ستتمكن من الوصول لجميع صفحات النظام

## ملاحظات مهمة

- فقط المستخدمين المضافين في Firebase Console يمكنهم تسجيل الدخول
- لا يوجد تسجيل مستخدمين جدد من التطبيق - يجب إضافتهم من Firebase Console
- تأكد من الحفاظ على أمان بيانات Firebase الخاصة بك
- لا تشارك مفاتيح API في أماكن عامة

## استكشاف الأخطاء

إذا واجهت مشاكل:

1. تأكد من صحة تكوين Firebase
2. تأكد من تفعيل Email/Password authentication
3. تأكد من وجود المستخدم في Firebase Console
4. تحقق من console المتصفح للأخطاء
