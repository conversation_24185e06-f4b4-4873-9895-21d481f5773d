# دليل المستخدم - نظام إدارة مصنع الكريمات

## 🚀 البدء السريع

### 1. تشغيل التطبيق
```bash
npm install
npm run dev
```

### 2. تسجيل الدخول
- افتح المتصفح على: http://localhost:8080
- أدخل بيانات تسجيل الدخول:
  - **اسم المستخدم**: `admin`
  - **كلمة المرور**: `خالد ناصر 612712`

### 3. استكشاف النظام
بعد تسجيل الدخول، ستجد القائمة الرئيسية تحتوي على:
- 📊 لوحة التحكم
- 🧴 المنتجات
- 🧪 الخامات
- 📈 التقارير
- ⚙️ الإعدادات

## 📊 لوحة التحكم

### الإحصائيات السريعة
- إجمالي المنتجات
- إجمالي الخامات
- الإنتاج اليومي
- حالة المخزون

### الأنشطة الأخيرة
- عرض آخر العمليات في النظام
- تتبع الإضافات والتعديلات

## 🧴 إدارة المنتجات

### إضافة منتج جديد
1. انقر على "إضافة منتج جديد"
2. املأ البيانات المطلوبة:
   - اسم المنتج
   - الفئة (كريمات الوجه، شامبو، إلخ)
   - الكمية
   - التكلفة (بالجنيه المصري)
   - السعر (بالجنيه المصري)
   - المواد الخام المستخدمة

### عرض المنتجات
- قائمة بجميع المنتجات
- عرض التكلفة والسعر والربح
- إمكانية التعديل والحذف

## 🧪 إدارة المواد الخام

### إضافة مادة خام جديدة
1. انقر على "إضافة مادة خام جديدة"
2. املأ البيانات:
   - اسم المادة
   - النوع (زيوت طبيعية، فيتامينات، **علب فارغة**، إلخ)
   - الكمية
   - الوحدة (لتر، كيلو، قطعة، إلخ)
   - التكلفة لكل وحدة (بالجنيه المصري)
   - المورد
   - تاريخ الانتهاء

### مراقبة المخزون
- عرض جميع المواد الخام
- تنبيهات لتواريخ الانتهاء القريبة
- حساب القيمة الإجمالية للمخزون

## 📈 التقارير والإحصائيات

### الملخص المالي
- إجمالي الإيرادات
- إجمالي التكاليف
- صافي الربح

### الرسوم البيانية
- الإنتاج الشهري
- توزيع فئات المنتجات
- تكاليف المواد الخام
- هوامش الربح
- حالة المخزون

## ⚙️ الإعدادات

### معلومات الشركة
- اسم الشركة
- العنوان
- رقم الهاتف
- البريد الإلكتروني
- الرقم الضريبي

### إعدادات المظهر
- تبديل الوضع الداكن/الفاتح
- اللغة (العربية فقط)

### بيانات تسجيل الدخول
- عرض بيانات الحساب الحالية
- البيانات ثابتة ولا يمكن تغييرها

### إدارة البيانات
- تصدير البيانات (JSON)
- استعادة الإعدادات الافتراضية

## 💡 نصائح مهمة

### الحفظ التلقائي
- جميع البيانات تحفظ تلقائياً في المتصفح
- لا حاجة لحفظ يدوي

### النسخ الاحتياطي
- استخدم "تصدير البيانات" من الإعدادات
- احفظ النسخة الاحتياطية في مكان آمن

### الأمان
- لا تشارك بيانات تسجيل الدخول
- أغلق المتصفح بعد الانتهاء من العمل

### العملة
- جميع الأسعار بالجنيه المصري (ج.م)
- التنسيق تلقائي للأرقام

## 🔧 استكشاف الأخطاء

### مشكلة في تسجيل الدخول
- تأكد من كتابة البيانات بدقة
- اسم المستخدم: `admin`
- كلمة المرور: `خالد ناصر 612712`

### فقدان البيانات
- البيانات محفوظة في المتصفح
- تجنب مسح بيانات المتصفح
- استخدم النسخ الاحتياطي بانتظام

### مشاكل في العرض
- جرب تحديث الصفحة (F5)
- تأكد من استخدام متصفح حديث
- امسح ذاكرة التخزين المؤقت

## 📞 الدعم

للحصول على المساعدة:
- راجع هذا الدليل
- تواصل مع المطور: Khaled Nasser
- تحقق من ملف README.md للتفاصيل التقنية
