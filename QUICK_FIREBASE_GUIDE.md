# دليل سريع لإعداد Firebase 🔥

## خطوات سريعة (5 دقائق)

### 1️⃣ إنشاء مشروع Firebase
1. اذهب إلى: https://console.firebase.google.com/
2. انقر "إضافة مشروع" (Add project)
3. اس<PERSON> المشروع: `cream-factory-manager`
4. اختر البلد: مصر
5. انقر "إنشاء مشروع"

### 2️⃣ تفعيل المصادقة
1. انقر "Authentication" من القائمة
2. انقر "البدء" (Get started)
3. انقر تبويب "Sign-in method"
4. انقر "Email/Password"
5. فعّل "Email/Password" فقط
6. انقر "حفظ"

### 3️⃣ إضافة تطبيق ويب
1. انقر أيقونة الويب `</>`
2. اسم التطبيق: `cream-factory-web`
3. انقر "تسجيل التطبيق"
4. **انسخ** الكود الذي يظهر

### 4️⃣ تحديث التطبيق
1. افتح ملف: `src/lib/firebase.ts`
2. استبدل القيم في `firebaseConfig`
3. احفظ الملف

### 5️⃣ إنشاء حساب المدير
1. اذهب إلى "Authentication" > "Users"
2. انقر "إضافة مستخدم" (Add user)
3. أدخل:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `خالد ناصر 612712`
4. انقر "إضافة مستخدم"

## ✅ اختبار النظام
1. شغّل التطبيق: `npm run dev`
2. اذهب إلى: http://localhost:8080
3. سجل دخول بالبيانات التي أنشأتها
4. استمتع بالنظام! 🎉

## 🔧 إذا واجهت مشاكل

### خطأ في التكوين
- تأكد من نسخ جميع القيم من Firebase Console
- تأكد من عدم وجود مسافات إضافية

### خطأ في تسجيل الدخول
- تأكد من تفعيل Email/Password في Firebase
- تأكد من إنشاء المستخدم في Firebase Console

### خطأ في الشبكة
- تأكد من الاتصال بالإنترنت
- تأكد من صحة Project ID

## 📞 تحتاج مساعدة؟
راجع الملف التفصيلي: `FIREBASE_SETUP.md`
