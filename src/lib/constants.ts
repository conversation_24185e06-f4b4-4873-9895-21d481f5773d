// إعدادات التطبيق الثابتة

// العملة
export const CURRENCY = {
  symbol: 'ج.م',
  name: 'الجنيه المصري',
  code: 'EGP'
};

// تنسيق الأرقام
export const formatCurrency = (amount: number): string => {
  return `${amount.toLocaleString('ar-EG')} ${CURRENCY.symbol}`;
};

// تنسيق التاريخ
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG');
};

// فئات المنتجات
export const PRODUCT_CATEGORIES = [
  'كريمات الوجه',
  'كريمات الجسم', 
  'شامبو',
  'صابون',
  'لوشن',
  'زيوت طبيعية',
  'أخرى'
];

// أنواع المواد الخام
export const MATERIAL_TYPES = [
  'زيوت طبيعية',
  'فيتامينات',
  'مواد كيميائية',
  'مستخلصات',
  'مواد حافظة',
  'عطور',
  'ملونات',
  'مثبتات',
  'علب فارغة'
];

// وحدات القياس
export const UNITS = [
  'لتر',
  'كيلو',
  'جرام',
  'مل',
  'قطعة',
  'علبة',
  'كيس'
];

// إعدادات اللغة
export const LANGUAGE_CONFIG = {
  locale: 'ar-EG',
  direction: 'rtl',
  name: 'العربية'
};
