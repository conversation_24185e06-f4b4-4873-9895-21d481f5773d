// خدمات قاعدة البيانات Firestore
import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy,
  onSnapshot,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

// أنواع البيانات
export interface Product {
  id?: string;
  name: string;
  category: string;
  quantity: number;
  cost: number;
  price: number;
  materials: string[];
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

export interface Material {
  id?: string;
  name: string;
  type: string;
  quantity: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  expiryDate: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// خدمات المنتجات
export const productService = {
  // إضافة منتج جديد
  async addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...product,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  },

  // جلب جميع المنتجات
  async getProducts(): Promise<Product[]> {
    try {
      const q = query(collection(db, 'products'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Product));
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error);
      throw error;
    }
  },

  // تحديث منتج
  async updateProduct(id: string, product: Partial<Product>) {
    try {
      const productRef = doc(db, 'products', id);
      await updateDoc(productRef, {
        ...product,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('خطأ في تحديث المنتج:', error);
      throw error;
    }
  },

  // حذف منتج
  async deleteProduct(id: string) {
    try {
      await deleteDoc(doc(db, 'products', id));
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      throw error;
    }
  },

  // الاستماع للتغييرات في الوقت الفعلي
  onProductsChange(callback: (products: Product[]) => void) {
    const q = query(collection(db, 'products'), orderBy('createdAt', 'desc'));
    return onSnapshot(q, (querySnapshot) => {
      const products = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Product));
      callback(products);
    });
  }
};

// خدمات المواد الخام
export const materialService = {
  // إضافة مادة خام جديدة
  async addMaterial(material: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      const docRef = await addDoc(collection(db, 'materials'), {
        ...material,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('خطأ في إضافة المادة الخام:', error);
      throw error;
    }
  },

  // جلب جميع المواد الخام
  async getMaterials(): Promise<Material[]> {
    try {
      const q = query(collection(db, 'materials'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Material));
    } catch (error) {
      console.error('خطأ في جلب المواد الخام:', error);
      throw error;
    }
  },

  // تحديث مادة خام
  async updateMaterial(id: string, material: Partial<Material>) {
    try {
      const materialRef = doc(db, 'materials', id);
      await updateDoc(materialRef, {
        ...material,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('خطأ في تحديث المادة الخام:', error);
      throw error;
    }
  },

  // حذف مادة خام
  async deleteMaterial(id: string) {
    try {
      await deleteDoc(doc(db, 'materials', id));
    } catch (error) {
      console.error('خطأ في حذف المادة الخام:', error);
      throw error;
    }
  },

  // الاستماع للتغييرات في الوقت الفعلي
  onMaterialsChange(callback: (materials: Material[]) => void) {
    const q = query(collection(db, 'materials'), orderBy('createdAt', 'desc'));
    return onSnapshot(q, (querySnapshot) => {
      const materials = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Material));
      callback(materials);
    });
  }
};

// خدمة عامة لحفظ إعدادات الشركة
export const settingsService = {
  async saveCompanyInfo(companyInfo: any) {
    try {
      const docRef = doc(db, 'settings', 'companyInfo');
      await updateDoc(docRef, {
        ...companyInfo,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      // إذا لم يكن الوثيقة موجودة، أنشئها
      try {
        await addDoc(collection(db, 'settings'), {
          ...companyInfo,
          id: 'companyInfo',
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      } catch (addError) {
        console.error('خطأ في حفظ معلومات الشركة:', addError);
        throw addError;
      }
    }
  },

  async getCompanyInfo() {
    try {
      const querySnapshot = await getDocs(collection(db, 'settings'));
      const companyDoc = querySnapshot.docs.find(doc => doc.data().id === 'companyInfo');
      return companyDoc ? companyDoc.data() : null;
    } catch (error) {
      console.error('خطأ في جلب معلومات الشركة:', error);
      throw error;
    }
  }
};
