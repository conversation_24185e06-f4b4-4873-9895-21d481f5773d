import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./components/SimpleAuthContext";
import { SimpleLayout } from "./components/SimpleLayout";
import SimplePrivateRoute from "./components/SimplePrivateRoute";
import Dashboard from "./pages/Dashboard";
import Products from "./pages/Products";
import Materials from "./pages/Materials";
import Reports from "./pages/Reports";
import SimpleSettings from "./pages/SimpleSettings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const SimpleApp = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <SimpleLayout>
            <Routes>
              <Route path="/" element={
                <SimplePrivateRoute>
                  <Dashboard />
                </SimplePrivateRoute>
              } />
              <Route path="/products" element={
                <SimplePrivateRoute>
                  <Products />
                </SimplePrivateRoute>
              } />
              <Route path="/materials" element={
                <SimplePrivateRoute>
                  <Materials />
                </SimplePrivateRoute>
              } />
              <Route path="/reports" element={
                <SimplePrivateRoute>
                  <Reports />
                </SimplePrivateRoute>
              } />
              <Route path="/settings" element={
                <SimplePrivateRoute>
                  <SimpleSettings />
                </SimplePrivateRoute>
              } />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </SimpleLayout>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default SimpleApp;
