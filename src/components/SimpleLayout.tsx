import React from 'react';
import { SimpleTopNavigation } from './SimpleTopNavigation';
import { useAuth } from './SimpleAuthContext';

interface LayoutProps {
  children: React.ReactNode;
}

export const SimpleLayout: React.FC<LayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <div className="w-full">{children}</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <SimpleTopNavigation />
      <main className="p-6">
        {children}
      </main>
    </div>
  );
};
