
import React from 'react';
import { TopNavigation } from './TopNavigation';
import { useAuth } from './AuthContext';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <div className="w-full">{children}</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <TopNavigation />
      <main className="p-6">
        {children}
      </main>
    </div>
  );
};
