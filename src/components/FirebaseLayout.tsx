import React from 'react';
import { FirebaseTopNavigation } from './FirebaseTopNavigation';
import { useAuth } from './FirebaseAuthContext';

interface LayoutProps {
  children: React.ReactNode;
}

export const FirebaseLayout: React.FC<LayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <div className="w-full">{children}</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <FirebaseTopNavigation />
      <main className="p-6">
        {children}
      </main>
    </div>
  );
};
