
import React, { createContext, useContext, useState, useEffect } from 'react';

interface AuthContextType {
  isAuthenticated: boolean;
  theme: string;
  login: (username: string, password: string) => boolean;
  logout: () => void;
  setTheme: (theme: string) => void;
  changePassword: (oldPassword: string, newPassword: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [theme, setTheme] = useState('light');

  useEffect(() => {
    const savedAuth = localStorage.getItem('isAuthenticated');
    const savedTheme = localStorage.getItem('theme');

    if (savedAuth === 'true') setIsAuthenticated(true);
    if (savedTheme) {
      setTheme(savedTheme);
    } else {
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const defaultTheme = systemPrefersDark ? 'dark' : 'light';
      setTheme(defaultTheme);
      localStorage.setItem('theme', defaultTheme);
    }
  }, []);

  useEffect(() => {
    const root = document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  const login = (username: string, password: string): boolean => {
    const defaultUsername = 'admin';
    const savedPassword = localStorage.getItem('adminPassword') || 'admin123';
    
    if (username === defaultUsername && password === savedPassword) {
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', 'true');
      return true;
    }
    return false;
  };

  const logout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
  };

  const handleSetTheme = (newTheme: string) => {
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  const changePassword = (oldPassword: string, newPassword: string): boolean => {
    const currentPassword = localStorage.getItem('adminPassword') || 'admin123';
    if (oldPassword === currentPassword) {
      localStorage.setItem('adminPassword', newPassword);
      return true;
    }
    return false;
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      theme,
      login,
      logout,
      setTheme: handleSetTheme,
      changePassword,
    }}>
      {children}
    </AuthContext.Provider>
  );
};
