import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from './SimpleAuthContext';
import { Moon, User, LogOut } from 'lucide-react';

const menuItems = [
  {
    title: "لوحة التحكم",
    url: "/",
    icon: "📊",
  },
  {
    title: "المنتجات",
    url: "/products",
    icon: "🧴",
  },
  {
    title: "الخامات",
    url: "/materials",
    icon: "🧪",
  },
  {
    title: "التقارير",
    url: "/reports",
    icon: "📈",
  },
  {
    title: "الإعدادات",
    url: "/settings",
    icon: "⚙️",
  },
];

export function SimpleTopNavigation() {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout, theme, setTheme } = useAuth();

  const handleLogout = () => {
    logout();
  };

  const handleNavigation = (url: string) => {
    navigate(url);
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <nav className="bg-card border-b border-border shadow-sm">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-xl font-bold text-primary">🏭 نظام إدارة المصنع</h1>
          </div>

          {/* Navigation Menu */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4 space-x-reverse">
              {menuItems.map((item) => (
                <button
                  key={item.title}
                  onClick={() => handleNavigation(item.url)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    location.pathname === item.url
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.title}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Right side - Theme toggle and User menu */}
          <div className="flex items-center gap-2">
            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="rounded-full"
            >
              <Moon className="h-5 w-5" />
              <span className="sr-only">تبديل الوضع الداكن</span>
            </Button>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <User className="h-5 w-5" />
                  <span className="sr-only">قائمة المستخدم</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem disabled>
                  <User className="mr-2 h-4 w-4" />
                  المدير
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/settings')}>
                  ⚙️ الإعدادات
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  تسجيل الخروج
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <span className="text-xl">☰</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {menuItems.map((item) => (
                  <DropdownMenuItem
                    key={item.title}
                    onClick={() => handleNavigation(item.url)}
                    className={location.pathname === item.url ? 'bg-accent' : ''}
                  >
                    <span className="mr-2">{item.icon}</span>
                    {item.title}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  );
}
