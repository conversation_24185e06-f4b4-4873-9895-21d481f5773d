
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { useAuth } from './AuthContext';

const menuItems = [
  {
    title: "لوحة التحكم",
    url: "/",
    icon: "📊",
  },
  {
    title: "المنتجات",
    url: "/products",
    icon: "🧴",
  },
  {
    title: "الخامات",
    url: "/materials",
    icon: "🧪",
  },
  {
    title: "التقارير",
    url: "/reports",
    icon: "📈",
  },
  {
    title: "الإعدادات",
    url: "/settings",
    icon: "⚙️",
  },
];

export function AppSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleNavigation = (url: string) => {
    navigate(url);
  };

  return (
    <Sidebar side="right">
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-xl font-bold text-primary mb-4">
            🏭 نظام إدارة المصنع
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    isActive={location.pathname === item.url}
                    onClick={() => handleNavigation(item.url)}
                    className="flex items-center gap-3 text-lg hover:bg-accent rounded-lg transition-colors cursor-pointer"
                  >
                    <span className="text-xl">{item.icon}</span>
                    <span className="font-medium">{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <Button 
          onClick={handleLogout}
          variant="outline" 
          className="w-full"
        >
          🚪 تسجيل الخروج
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}
