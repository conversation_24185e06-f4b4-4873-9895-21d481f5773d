
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface Material {
  id: number;
  name: string;
  type: string;
  quantity: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  expiryDate: string;
}

const Materials = () => {
  const { toast } = useToast();
  const [materials, setMaterials] = useState<Material[]>([
    {
      id: 1,
      name: 'زيت الورد الطبيعي',
      type: 'زيوت طبيعية',
      quantity: 50,
      unit: 'لتر',
      costPerUnit: 45,
      supplier: 'شركة الزيوت الطبيعية',
      expiryDate: '2025-12-31'
    },
    {
      id: 2,
      name: 'فيتامين E',
      type: 'فيتامينات',
      quantity: 25,
      unit: 'كيلو',
      costPerUnit: 120,
      supplier: 'شركة المكملات الغذائية',
      expiryDate: '2025-06-30'
    },
    {
      id: 3,
      name: 'حمض الهيالورونيك',
      type: 'مواد كيميائية',
      quantity: 10,
      unit: 'كيلو',
      costPerUnit: 300,
      supplier: 'شركة المواد الكيميائية المتطورة',
      expiryDate: '2025-09-15'
    }
  ]);

  const [newMaterial, setNewMaterial] = useState<Omit<Material, 'id'>>({
    name: '',
    type: '',
    quantity: 0,
    unit: '',
    costPerUnit: 0,
    supplier: '',
    expiryDate: ''
  });

  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const materialTypes = ['زيوت طبيعية', 'فيتامينات', 'مواد كيميائية', 'مستخلصات', 'مواد حافظة', 'عطور'];
  const units = ['لتر', 'كيلو', 'جرام', 'مل', 'قطعة'];

  const handleAddMaterial = () => {
    if (newMaterial.name && newMaterial.type) {
      const material: Material = {
        ...newMaterial,
        id: Date.now()
      };
      setMaterials([...materials, material]);
      setNewMaterial({ 
        name: '', type: '', quantity: 0, unit: '', 
        costPerUnit: 0, supplier: '', expiryDate: '' 
      });
      setIsDialogOpen(false);
      toast({ 
        title: "تم إضافة المادة الخام بنجاح", 
        description: `تم إضافة ${material.name} إلى قائمة المواد الخام` 
      });
    }
  };

  const handleEditMaterial = () => {
    if (editingMaterial) {
      setMaterials(materials.map(m => m.id === editingMaterial.id ? editingMaterial : m));
      setEditingMaterial(null);
      setIsDialogOpen(false);
      toast({ 
        title: "تم تحديث المادة الخام بنجاح", 
        description: `تم تحديث ${editingMaterial.name}` 
      });
    }
  };

  const handleDeleteMaterial = (id: number) => {
    setMaterials(materials.filter(m => m.id !== id));
    toast({ title: "تم حذف المادة الخام", description: "تم حذف المادة الخام من القائمة" });
  };

  const openEditDialog = (material: Material) => {
    setEditingMaterial(material);
    setIsDialogOpen(true);
  };

  const openAddDialog = () => {
    setEditingMaterial(null);
    setNewMaterial({ 
      name: '', type: '', quantity: 0, unit: '', 
      costPerUnit: 0, supplier: '', expiryDate: '' 
    });
    setIsDialogOpen(true);
  };

  const currentMaterial = editingMaterial || newMaterial;

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30;
  };

  const getTotalValue = (material: Material) => {
    return (material.quantity * material.costPerUnit).toFixed(2);
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">🧪 إدارة المواد الخام</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openAddDialog} className="gap-2">
              ➕ إضافة مادة خام جديدة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingMaterial ? '✏️ تعديل المادة الخام' : '➕ إضافة مادة خام جديدة'}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">اسم المادة الخام</Label>
                <Input
                  id="name"
                  value={currentMaterial.name}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, name: e.target.value})
                    : setNewMaterial({...newMaterial, name: e.target.value})
                  }
                  placeholder="أدخل اسم المادة الخام"
                />
              </div>
              <div>
                <Label htmlFor="type">النوع</Label>
                <select
                  id="type"
                  value={currentMaterial.type}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, type: e.target.value})
                    : setNewMaterial({...newMaterial, type: e.target.value})
                  }
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">اختر النوع</option>
                  {materialTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quantity">الكمية</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={currentMaterial.quantity}
                    onChange={(e) => editingMaterial 
                      ? setEditingMaterial({...editingMaterial, quantity: Number(e.target.value)})
                      : setNewMaterial({...newMaterial, quantity: Number(e.target.value)})
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="unit">الوحدة</Label>
                  <select
                    id="unit"
                    value={currentMaterial.unit}
                    onChange={(e) => editingMaterial 
                      ? setEditingMaterial({...editingMaterial, unit: e.target.value})
                      : setNewMaterial({...newMaterial, unit: e.target.value})
                    }
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">اختر الوحدة</option>
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>
              </div>
              <div>
                <Label htmlFor="costPerUnit">التكلفة لكل وحدة</Label>
                <Input
                  id="costPerUnit"
                  type="number"
                  step="0.01"
                  value={currentMaterial.costPerUnit}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, costPerUnit: Number(e.target.value)})
                    : setNewMaterial({...newMaterial, costPerUnit: Number(e.target.value)})
                  }
                />
              </div>
              <div>
                <Label htmlFor="supplier">المورد</Label>
                <Input
                  id="supplier"
                  value={currentMaterial.supplier}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, supplier: e.target.value})
                    : setNewMaterial({...newMaterial, supplier: e.target.value})
                  }
                  placeholder="اسم المورد"
                />
              </div>
              <div>
                <Label htmlFor="expiryDate">تاريخ الانتهاء</Label>
                <Input
                  id="expiryDate"
                  type="date"
                  value={currentMaterial.expiryDate}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, expiryDate: e.target.value})
                    : setNewMaterial({...newMaterial, expiryDate: e.target.value})
                  }
                />
              </div>
              <Button 
                onClick={editingMaterial ? handleEditMaterial : handleAddMaterial}
                className="w-full"
              >
                {editingMaterial ? '💾 حفظ التغييرات' : '➕ إضافة المادة الخام'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {materials.map((material) => (
          <Card key={material.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{material.name}</CardTitle>
                {isExpiringSoon(material.expiryDate) && (
                  <Badge variant="destructive" className="text-xs">⚠️ ينتهي قريباً</Badge>
                )}
              </div>
              <Badge variant="secondary">{material.type}</Badge>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-muted-foreground">الكمية:</span>
                  <span className="font-medium mr-2">{material.quantity} {material.unit}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">التكلفة/وحدة:</span>
                  <span className="font-medium mr-2">{material.costPerUnit} ر.س</span>
                </div>
                <div className="col-span-2">
                  <span className="text-muted-foreground">القيمة الإجمالية:</span>
                  <span className="font-medium mr-2 text-green-600">
                    {getTotalValue(material)} ر.س
                  </span>
                </div>
              </div>
              
              <div className="text-sm">
                <p><span className="text-muted-foreground">المورد:</span> {material.supplier}</p>
                <p>
                  <span className="text-muted-foreground">تاريخ الانتهاء:</span> 
                  <span className={isExpiringSoon(material.expiryDate) ? 'text-red-600 font-medium' : ''}>
                    {new Date(material.expiryDate).toLocaleDateString('ar-SA')}
                  </span>
                </p>
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => openEditDialog(material)}
                  className="flex-1"
                >
                  ✏️ تعديل
                </Button>
                <Button 
                  size="sm" 
                  variant="destructive"
                  onClick={() => handleDeleteMaterial(material.id)}
                  className="flex-1"
                >
                  🗑️ حذف
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {materials.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">🧪</div>
            <h3 className="text-xl font-medium mb-2">لا توجد مواد خام</h3>
            <p className="text-muted-foreground mb-4">ابدأ بإضافة أول مادة خام لمصنعك</p>
            <Button onClick={openAddDialog}>➕ إضافة مادة خام جديدة</Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Materials;
