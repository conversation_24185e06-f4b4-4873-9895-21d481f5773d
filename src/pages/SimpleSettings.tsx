import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useAuth } from '../components/FirebaseAuthContext';
import { useToast } from "@/hooks/use-toast";

const SimpleSettings = () => {
  const { theme, setTheme } = useAuth();
  const { toast } = useToast();

  const [companyInfo, setCompanyInfo] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    taxNumber: ''
  });

  const handleCompanyInfoSave = () => {
    localStorage.setItem('companyInfo', JSON.stringify(companyInfo));
    toast({
      title: "تم حفظ معلومات الشركة",
      description: "تم تحديث معلومات الشركة بنجاح"
    });
  };

  const exportData = () => {
    const data = {
      products: JSON.parse(localStorage.getItem('products') || '[]'),
      materials: JSON.parse(localStorage.getItem('materials') || '[]'),
      companyInfo: JSON.parse(localStorage.getItem('companyInfo') || '{}')
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `factory-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: "تم تصدير البيانات",
      description: "تم تصدير جميع البيانات بنجاح"
    });
  };

  const resetToDefaults = () => {
    setTheme('light');
    toast({
      title: "تم استعادة الإعدادات الافتراضية",
      description: "تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية"
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">⚙️ الإعدادات</h1>
        <p className="text-muted-foreground">إدارة إعدادات النظام والشركة</p>
      </div>

      <div className="grid gap-6">
        {/* معلومات الشركة */}
        <Card>
          <CardHeader>
            <CardTitle>🏢 معلومات الشركة</CardTitle>
            <CardDescription>
              تحديث معلومات الشركة الأساسية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>اسم الشركة</Label>
                <Input
                  value={companyInfo.name}
                  onChange={(e) => setCompanyInfo({...companyInfo, name: e.target.value})}
                  placeholder="اسم الشركة"
                />
              </div>
              <div className="space-y-2">
                <Label>رقم الهاتف</Label>
                <Input
                  value={companyInfo.phone}
                  onChange={(e) => setCompanyInfo({...companyInfo, phone: e.target.value})}
                  placeholder="رقم الهاتف"
                />
              </div>
              <div className="space-y-2">
                <Label>البريد الإلكتروني</Label>
                <Input
                  value={companyInfo.email}
                  onChange={(e) => setCompanyInfo({...companyInfo, email: e.target.value})}
                  placeholder="البريد الإلكتروني"
                />
              </div>
              <div className="space-y-2">
                <Label>الرقم الضريبي</Label>
                <Input
                  value={companyInfo.taxNumber}
                  onChange={(e) => setCompanyInfo({...companyInfo, taxNumber: e.target.value})}
                  placeholder="الرقم الضريبي"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>العنوان</Label>
              <Input
                value={companyInfo.address}
                onChange={(e) => setCompanyInfo({...companyInfo, address: e.target.value})}
                placeholder="عنوان الشركة"
              />
            </div>
            <Button onClick={handleCompanyInfoSave} className="w-full">
              💾 حفظ معلومات الشركة
            </Button>
          </CardContent>
        </Card>

        {/* إعدادات المظهر */}
        <Card>
          <CardHeader>
            <CardTitle>🎨 إعدادات المظهر</CardTitle>
            <CardDescription>
              تخصيص مظهر التطبيق
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>اللغة</Label>
              <div className="p-2 border rounded-md bg-muted">
                <span className="text-sm">العربية (افتراضي)</span>
              </div>
              <p className="text-xs text-muted-foreground">التطبيق يعمل باللغة العربية فقط</p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>الوضع الداكن</Label>
                <p className="text-sm text-muted-foreground">
                  تفعيل الوضع الداكن للتطبيق
                </p>
              </div>
              <Switch
                checked={theme === 'dark'}
                onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
              />
            </div>
          </CardContent>
        </Card>

        {/* إدارة البيانات */}
        <Card>
          <CardHeader>
            <CardTitle>💾 إدارة البيانات</CardTitle>
            <CardDescription>
              نسخ احتياطي واستعادة البيانات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={exportData} variant="outline" className="w-full">
              📤 تصدير البيانات
            </Button>
            
            <div className="pt-4 border-t">
              <p className="text-sm text-muted-foreground mb-2">
                آخر نسخة احتياطية: {new Date().toLocaleDateString('en-US')}
              </p>
              <Button onClick={resetToDefaults} variant="destructive" className="w-full">
                🔄 استعادة الإعدادات الافتراضية
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* معلومات النظام */}
        <Card>
          <CardHeader>
            <CardTitle>ℹ️ معلومات النظام</CardTitle>
            <CardDescription>
              تفاصيل النظام والإصدار
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">الإصدار</p>
                <p className="font-medium">1.0.0</p>
              </div>
              <div>
                <p className="text-muted-foreground">آخر تحديث</p>
                <p className="font-medium">{new Date().toLocaleDateString('en-US')}</p>
              </div>
              <div>
                <p className="text-muted-foreground">المطور</p>
                <p className="font-medium">Khaled Nasser</p>
              </div>
              <div>
                <p className="text-muted-foreground">العملة</p>
                <p className="font-medium">الجنيه المصري (ج.م)</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SimpleSettings;
