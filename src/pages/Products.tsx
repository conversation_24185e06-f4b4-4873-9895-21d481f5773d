
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { PRODUCT_CATEGORIES, formatCurrency } from "@/lib/constants";
import { productService, Product } from "@/lib/firestore";

// تم نقل interface Product إلى firestore.ts

const Products = () => {
  const { toast } = useToast();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  const [newProduct, setNewProduct] = useState<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>({
    name: '',
    category: '',
    quantity: 0,
    cost: 0,
    price: 0,
    materials: []
  });

  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [materialsInput, setMaterialsInput] = useState('');

  const categories = PRODUCT_CATEGORIES;

  // جلب المنتجات من Firestore
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const productsData = await productService.getProducts();
        setProducts(productsData);
      } catch (error) {
        toast({
          title: "خطأ في جلب المنتجات",
          description: "حدث خطأ أثناء جلب البيانات من قاعدة البيانات",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadProducts();

    // الاستماع للتغييرات في الوقت الفعلي
    const unsubscribe = productService.onProductsChange((updatedProducts) => {
      setProducts(updatedProducts);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [toast]);

  const handleAddProduct = async () => {
    if (newProduct.name && newProduct.category) {
      const product: Product = {
        ...newProduct,
        id: Date.now(),
        materials: materialsInput.split(',').map(m => m.trim()).filter(m => m)
      };
      setProducts([...products, product]);
      setNewProduct({ name: '', category: '', quantity: 0, cost: 0, price: 0, materials: [] });
      setMaterialsInput('');
      setIsDialogOpen(false);
      toast({ title: "تم إضافة المنتج بنجاح", description: `تم إضافة ${product.name} إلى قائمة المنتجات` });
    }
  };

  const handleEditProduct = () => {
    if (editingProduct) {
      const updatedProduct = {
        ...editingProduct,
        materials: materialsInput.split(',').map(m => m.trim()).filter(m => m)
      };
      setProducts(products.map(p => p.id === editingProduct.id ? updatedProduct : p));
      setEditingProduct(null);
      setMaterialsInput('');
      setIsDialogOpen(false);
      toast({ title: "تم تحديث المنتج بنجاح", description: `تم تحديث ${updatedProduct.name}` });
    }
  };

  const handleDeleteProduct = (id: number) => {
    setProducts(products.filter(p => p.id !== id));
    toast({ title: "تم حذف المنتج", description: "تم حذف المنتج من القائمة" });
  };

  const openEditDialog = (product: Product) => {
    setEditingProduct(product);
    setMaterialsInput(product.materials.join(', '));
    setIsDialogOpen(true);
  };

  const openAddDialog = () => {
    setEditingProduct(null);
    setNewProduct({ name: '', category: '', quantity: 0, cost: 0, price: 0, materials: [] });
    setMaterialsInput('');
    setIsDialogOpen(true);
  };

  const currentProduct = editingProduct || newProduct;

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">🧴 إدارة المنتجات</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openAddDialog} className="gap-2">
              ➕ إضافة منتج جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingProduct ? '✏️ تعديل المنتج' : '➕ إضافة منتج جديد'}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">اسم المنتج</Label>
                <Input
                  id="name"
                  value={currentProduct.name}
                  onChange={(e) => editingProduct 
                    ? setEditingProduct({...editingProduct, name: e.target.value})
                    : setNewProduct({...newProduct, name: e.target.value})
                  }
                  placeholder="أدخل اسم المنتج"
                />
              </div>
              <div>
                <Label htmlFor="category">الفئة</Label>
                <select
                  id="category"
                  value={currentProduct.category}
                  onChange={(e) => editingProduct 
                    ? setEditingProduct({...editingProduct, category: e.target.value})
                    : setNewProduct({...newProduct, category: e.target.value})
                  }
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">اختر الفئة</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="quantity">الكمية</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={currentProduct.quantity}
                    onChange={(e) => editingProduct 
                      ? setEditingProduct({...editingProduct, quantity: Number(e.target.value)})
                      : setNewProduct({...newProduct, quantity: Number(e.target.value)})
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="cost">التكلفة</Label>
                  <Input
                    id="cost"
                    type="number"
                    step="0.01"
                    value={currentProduct.cost}
                    onChange={(e) => editingProduct 
                      ? setEditingProduct({...editingProduct, cost: Number(e.target.value)})
                      : setNewProduct({...newProduct, cost: Number(e.target.value)})
                    }
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="price">سعر البيع</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  value={currentProduct.price}
                  onChange={(e) => editingProduct 
                    ? setEditingProduct({...editingProduct, price: Number(e.target.value)})
                    : setNewProduct({...newProduct, price: Number(e.target.value)})
                  }
                />
              </div>
              <div>
                <Label htmlFor="materials">المواد الخام (مفصولة بفاصلة)</Label>
                <Input
                  id="materials"
                  value={materialsInput}
                  onChange={(e) => setMaterialsInput(e.target.value)}
                  placeholder="زيت الورد, فيتامين E, حمض الهيالورونيك"
                />
              </div>
              <Button 
                onClick={editingProduct ? handleEditProduct : handleAddProduct}
                className="w-full"
              >
                {editingProduct ? '💾 حفظ التغييرات' : '➕ إضافة المنتج'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {products.map((product) => (
          <Card key={product.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">{product.name}</CardTitle>
              <Badge variant="secondary">{product.category}</Badge>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-muted-foreground">الكمية:</span>
                  <span className="font-medium mr-2">{product.quantity}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">التكلفة:</span>
                  <span className="font-medium mr-2">{formatCurrency(product.cost)}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">السعر:</span>
                  <span className="font-medium mr-2">{formatCurrency(product.price)}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">الربح:</span>
                  <span className="font-medium mr-2 text-green-600">
                    {formatCurrency(product.price - product.cost)}
                  </span>
                </div>
              </div>
              
              {product.materials.length > 0 && (
                <div>
                  <p className="text-sm text-muted-foreground mb-2">المواد الخام:</p>
                  <div className="flex flex-wrap gap-1">
                    {product.materials.map((material, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {material}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => openEditDialog(product)}
                  className="flex-1"
                >
                  ✏️ تعديل
                </Button>
                <Button 
                  size="sm" 
                  variant="destructive"
                  onClick={() => handleDeleteProduct(product.id)}
                  className="flex-1"
                >
                  🗑️ حذف
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {products.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-medium mb-2">لا توجد منتجات</h3>
            <p className="text-muted-foreground mb-4">ابدأ بإضافة أول منتج لمصنعك</p>
            <Button onClick={openAddDialog}>➕ إضافة منتج جديد</Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Products;
