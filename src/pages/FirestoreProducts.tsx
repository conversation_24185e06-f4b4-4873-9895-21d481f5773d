import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
// تم إزالة Select المعطل
import { useToast } from "@/hooks/use-toast";
import { PRODUCT_CATEGORIES, formatCurrency } from "@/lib/constants";
import { productService, Product } from "@/lib/firestore";

const FirestoreProducts = () => {
  const { toast } = useToast();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  const [newProduct, setNewProduct] = useState<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>({
    name: '',
    category: '',
    quantity: 0,
    cost: 0,
    price: 0,
    materials: []
  });

  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [materialsInput, setMaterialsInput] = useState('');

  const categories = PRODUCT_CATEGORIES;

  // جلب المنتجات من Firestore
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const productsData = await productService.getProducts();
        setProducts(productsData);
      } catch (error) {
        toast({
          title: "خطأ في جلب المنتجات",
          description: "حدث خطأ أثناء جلب البيانات من قاعدة البيانات",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadProducts();

    // الاستماع للتغييرات في الوقت الفعلي
    const unsubscribe = productService.onProductsChange((updatedProducts) => {
      setProducts(updatedProducts);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [toast]);

  const handleAddProduct = async () => {
    if (newProduct.name && newProduct.category) {
      try {
        const productData = {
          ...newProduct,
          materials: materialsInput.split(',').map(m => m.trim()).filter(m => m)
        };
        
        await productService.addProduct(productData);
        
        setNewProduct({
          name: '',
          category: '',
          quantity: 0,
          cost: 0,
          price: 0,
          materials: []
        });
        setMaterialsInput('');
        setIsDialogOpen(false);
        
        toast({
          title: "تم إضافة المنتج",
          description: `تم إضافة ${productData.name} بنجاح`
        });
      } catch (error) {
        toast({
          title: "خطأ في إضافة المنتج",
          description: "حدث خطأ أثناء حفظ المنتج",
          variant: "destructive"
        });
      }
    }
  };

  const handleEditProduct = async () => {
    if (editingProduct && editingProduct.id) {
      try {
        const updatedData = {
          ...editingProduct,
          materials: materialsInput.split(',').map(m => m.trim()).filter(m => m)
        };
        
        await productService.updateProduct(editingProduct.id, updatedData);
        
        setEditingProduct(null);
        setMaterialsInput('');
        setIsDialogOpen(false);
        
        toast({
          title: "تم تحديث المنتج",
          description: `تم تحديث ${updatedData.name} بنجاح`
        });
      } catch (error) {
        toast({
          title: "خطأ في تحديث المنتج",
          description: "حدث خطأ أثناء تحديث المنتج",
          variant: "destructive"
        });
      }
    }
  };

  const handleDeleteProduct = async (id: string) => {
    try {
      await productService.deleteProduct(id);
      toast({
        title: "تم حذف المنتج",
        description: "تم حذف المنتج بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ في حذف المنتج",
        description: "حدث خطأ أثناء حذف المنتج",
        variant: "destructive"
      });
    }
  };

  const openEditDialog = (product: Product) => {
    // نسخ جميع بيانات المنتج للتعديل
    setEditingProduct({
      ...product,
      name: product.name,
      category: product.category,
      quantity: product.quantity,
      cost: product.cost,
      price: product.price,
      materials: product.materials
    });
    setMaterialsInput(product.materials.join(', '));
    setIsDialogOpen(true);
  };

  const resetForm = () => {
    setNewProduct({
      name: '',
      category: '',
      quantity: 0,
      cost: 0,
      price: 0,
      materials: []
    });
    setEditingProduct(null);
    setMaterialsInput('');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">🧴 إدارة المنتجات</h1>
          <p className="text-muted-foreground">جاري تحميل المنتجات...</p>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">🧴 إدارة المنتجات</h1>
          <p className="text-muted-foreground">إدارة منتجات المصنع وتتبع المخزون</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>➕ إضافة منتج جديد</Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>اسم المنتج</Label>
                <Input
                  value={editingProduct ? editingProduct.name : newProduct.name}
                  onChange={(e) => editingProduct 
                    ? setEditingProduct({...editingProduct, name: e.target.value})
                    : setNewProduct({...newProduct, name: e.target.value})
                  }
                  placeholder="أدخل اسم المنتج"
                />
              </div>
              
              <div className="space-y-2">
                <Label>الفئة</Label>
                <select
                  value={editingProduct ? editingProduct.category : newProduct.category}
                  onChange={(e) => editingProduct
                    ? setEditingProduct({...editingProduct, category: e.target.value})
                    : setNewProduct({...newProduct, category: e.target.value})
                  }
                  className="w-full p-3 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                >
                  <option value="">اختر الفئة</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>الكمية</Label>
                  <Input
                    type="number"
                    value={editingProduct ? editingProduct.quantity : newProduct.quantity}
                    onChange={(e) => editingProduct
                      ? setEditingProduct({...editingProduct, quantity: Number(e.target.value)})
                      : setNewProduct({...newProduct, quantity: Number(e.target.value)})
                    }
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>التكلفة (ج.م)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={editingProduct ? editingProduct.cost : newProduct.cost}
                    onChange={(e) => editingProduct
                      ? setEditingProduct({...editingProduct, cost: Number(e.target.value)})
                      : setNewProduct({...newProduct, cost: Number(e.target.value)})
                    }
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>السعر (ج.م)</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingProduct ? editingProduct.price : newProduct.price}
                  onChange={(e) => editingProduct
                    ? setEditingProduct({...editingProduct, price: Number(e.target.value)})
                    : setNewProduct({...newProduct, price: Number(e.target.value)})
                  }
                />
              </div>
              
              <div className="space-y-2">
                <Label>المواد الخام (مفصولة بفاصلة)</Label>
                <Input
                  value={materialsInput}
                  onChange={(e) => setMaterialsInput(e.target.value)}
                  placeholder="زيت الورد, فيتامين E, حمض الهيالورونيك"
                />
              </div>
              
              <Button 
                onClick={editingProduct ? handleEditProduct : handleAddProduct}
                className="w-full"
              >
                {editingProduct ? "تحديث المنتج" : "إضافة المنتج"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {products.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">🧴</div>
            <h3 className="text-lg font-medium mb-2">لا توجد منتجات حتى الآن</h3>
            <p className="text-muted-foreground mb-4">ابدأ بإضافة منتجات جديدة لمصنعك</p>
            <Button onClick={() => setIsDialogOpen(true)}>
              ➕ إضافة أول منتج
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {products.map((product) => (
            <Card key={product.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{product.name}</CardTitle>
                    <Badge variant="secondary" className="mt-1">
                      {product.category}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(product)}
                    >
                      ✏️ تعديل
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => product.id && handleDeleteProduct(product.id)}
                    >
                      🗑️ حذف
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <span className="text-muted-foreground">الكمية:</span>
                    <span className="font-medium mr-2">{product.quantity} وحدة</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">التكلفة:</span>
                    <span className="font-medium mr-2">{formatCurrency(product.cost)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">السعر:</span>
                    <span className="font-medium mr-2">{formatCurrency(product.price)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">الربح:</span>
                    <span className="font-medium mr-2 text-green-600">
                      {formatCurrency(product.price - product.cost)}
                    </span>
                  </div>
                </div>
                
                {product.materials.length > 0 && (
                  <div>
                    <span className="text-muted-foreground">المواد الخام:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {product.materials.map((material, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {material}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FirestoreProducts;
