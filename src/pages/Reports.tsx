
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';

const Reports = () => {
  const [dateRange, setDateRange] = useState({
    from: '2024-01-01',
    to: '2024-12-31'
  });

  // Sample data for reports
  const monthlyProduction = [
    { month: 'يناير', production: 4000, cost: 50000, revenue: 75000 },
    { month: 'فبراير', production: 3000, cost: 37500, revenue: 56250 },
    { month: 'مارس', production: 5000, cost: 62500, revenue: 93750 },
    { month: 'أبريل', production: 4500, cost: 56250, revenue: 84375 },
    { month: 'مايو', production: 6000, cost: 75000, revenue: 112500 },
    { month: 'يونيو', production: 5500, cost: 68750, revenue: 103125 },
  ];

  const productCategoryData = [
    { name: 'كريمات الوجه', value: 35, revenue: 145000, color: '#0088FE' },
    { name: 'كريمات الجسم', value: 25, revenue: 98000, color: '#00C49F' },
    { name: 'شامبو', value: 20, revenue: 76000, color: '#FFBB28' },
    { name: 'صابون', value: 15, revenue: 45000, color: '#FF8042' },
    { name: 'أخرى', value: 5, revenue: 18000, color: '#8884D8' },
  ];

  const materialCosts = [
    { material: 'زيت الورد', cost: 12500, percentage: 25 },
    { material: 'فيتامين E', cost: 8750, percentage: 17.5 },
    { material: 'حمض الهيالورونيك', cost: 7500, percentage: 15 },
    { material: 'زيت جوز الهند', cost: 6250, percentage: 12.5 },
    { material: 'مستخلص البابونج', cost: 5000, percentage: 10 },
    { material: 'أخرى', cost: 10000, percentage: 20 },
  ];

  const profitMarginData = [
    { month: 'يناير', margin: 33.3 },
    { month: 'فبراير', margin: 33.3 },
    { month: 'مارس', margin: 33.3 },
    { month: 'أبريل', margin: 33.3 },
    { month: 'مايو', margin: 33.3 },
    { month: 'يونيو', margin: 33.3 },
  ];

  const inventoryStatus = [
    { product: 'كريم مرطب للوجه', current: 150, minimum: 100, status: 'good' },
    { product: 'شامبو الأطفال', current: 80, minimum: 100, status: 'low' },
    { product: 'كريم واقي الشمس', current: 200, minimum: 150, status: 'good' },
    { product: 'صابون طبيعي', current: 50, minimum: 75, status: 'critical' },
  ];

  const exportReport = (reportType: string) => {
    console.log(`Exporting ${reportType} report...`);
    // Here you would implement actual export functionality
  };

  const totalRevenue = productCategoryData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCosts = materialCosts.reduce((sum, item) => sum + item.cost, 0);
  const totalProfit = totalRevenue - totalCosts;

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">📈 التقارير والإحصائيات</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => exportReport('monthly')}>
            📊 تصدير التقرير الشهري
          </Button>
          <Button variant="outline" onClick={() => exportReport('inventory')}>
            📦 تصدير تقرير المخزون
          </Button>
        </div>
      </div>

      {/* Date Range Filter */}
      <Card>
        <CardHeader>
          <CardTitle>📅 فترة التقرير</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div>
              <Label htmlFor="from">من تاريخ</Label>
              <Input
                id="from"
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange({...dateRange, from: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="to">إلى تاريخ</Label>
              <Input
                id="to"
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange({...dateRange, to: e.target.value})}
              />
            </div>
            <Button>🔍 تحديث التقرير</Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">{totalRevenue.toLocaleString()} ر.س</p>
              </div>
              <div className="text-3xl">💰</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">إجمالي التكاليف</p>
                <p className="text-2xl font-bold text-red-600">{totalCosts.toLocaleString()} ر.س</p>
              </div>
              <div className="text-3xl">💸</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">صافي الربح</p>
                <p className="text-2xl font-bold text-blue-600">{totalProfit.toLocaleString()} ر.س</p>
              </div>
              <div className="text-3xl">📊</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">هامش الربح</p>
                <p className="text-2xl font-bold text-purple-600">
                  {((totalProfit / totalRevenue) * 100).toFixed(1)}%
                </p>
              </div>
              <div className="text-3xl">📈</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Monthly Production & Revenue */}
        <Card>
          <CardHeader>
            <CardTitle>📊 الإنتاج والإيرادات الشهرية</CardTitle>
            <CardDescription>مقارنة الإنتاج والإيرادات خلال الأشهر الماضية</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyProduction}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stackId="1" 
                  stroke="#8884d8" 
                  fill="#8884d8" 
                  name="الإيرادات"
                />
                <Area 
                  type="monotone" 
                  dataKey="cost" 
                  stackId="1" 
                  stroke="#82ca9d" 
                  fill="#82ca9d" 
                  name="التكاليف"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Product Categories Revenue */}
        <Card>
          <CardHeader>
            <CardTitle>🥧 إيرادات فئات المنتجات</CardTitle>
            <CardDescription>توزيع الإيرادات حسب فئة المنتج</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={productCategoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="revenue"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {productCategoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value.toLocaleString()} ر.س`, 'الإيرادات']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Material Costs */}
        <Card>
          <CardHeader>
            <CardTitle>🧪 تكاليف المواد الخام</CardTitle>
            <CardDescription>توزيع تكاليف المواد الخام</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={materialCosts}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="material" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value.toLocaleString()} ر.س`, 'التكلفة']} />
                <Bar dataKey="cost" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Profit Margin Trend */}
        <Card>
          <CardHeader>
            <CardTitle>📈 اتجاه هامش الربح</CardTitle>
            <CardDescription>تطور هامش الربح عبر الأشهر</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={profitMarginData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'هامش الربح']} />
                <Line 
                  type="monotone" 
                  dataKey="margin" 
                  stroke="#8884d8" 
                  strokeWidth={3}
                  dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Status */}
      <Card>
        <CardHeader>
          <CardTitle>📦 حالة المخزون</CardTitle>
          <CardDescription>المنتجات التي تحتاج إلى إعادة تخزين</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {inventoryStatus.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    item.status === 'good' ? 'bg-green-500' :
                    item.status === 'low' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <div>
                    <p className="font-medium">{item.product}</p>
                    <p className="text-sm text-muted-foreground">
                      الحد الأدنى: {item.minimum} وحدة
                    </p>
                  </div>
                </div>
                <div className="text-left">
                  <p className={`font-bold ${
                    item.status === 'good' ? 'text-green-600' :
                    item.status === 'low' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {item.current} وحدة
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {item.status === 'good' ? '✅ مناسب' :
                     item.status === 'low' ? '⚠️ منخفض' : '🚨 حرج'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;
