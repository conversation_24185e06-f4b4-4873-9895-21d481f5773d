
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';
import { formatCurrency } from "@/lib/constants";

const Reports = () => {
  const [dateRange, setDateRange] = useState({
    from: '2024-01-01',
    to: '2024-12-31'
  });

  // بيانات فارغة - ستملأ من البيانات الحقيقية
  const monthlyProduction: { month: string; production: number; cost: number; revenue: number }[] = [];

  const productCategoryData: { name: string; value: number; revenue: number; color: string }[] = [];

  const materialCosts: { material: string; cost: number; percentage: number }[] = [];

  const profitMarginData: { month: string; margin: number }[] = [];

  const inventoryStatus: { product: string; current: number; minimum: number; status: string }[] = [];

  const exportReport = (reportType: string) => {
    console.log(`Exporting ${reportType} report...`);
    // Here you would implement actual export functionality
  };

  const totalRevenue = productCategoryData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCosts = materialCosts.reduce((sum, item) => sum + item.cost, 0);
  const totalProfit = totalRevenue - totalCosts;

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">📈 التقارير والإحصائيات</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => exportReport('monthly')}>
            📊 تصدير التقرير الشهري
          </Button>
          <Button variant="outline" onClick={() => exportReport('inventory')}>
            📦 تصدير تقرير المخزون
          </Button>
        </div>
      </div>

      {/* Date Range Filter */}
      <Card>
        <CardHeader>
          <CardTitle>📅 فترة التقرير</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div>
              <Label htmlFor="from">من تاريخ</Label>
              <Input
                id="from"
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange({...dateRange, from: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="to">إلى تاريخ</Label>
              <Input
                id="to"
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange({...dateRange, to: e.target.value})}
              />
            </div>
            <Button>🔍 تحديث التقرير</Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(totalRevenue)}</p>
              </div>
              <div className="text-3xl">💰</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">إجمالي التكاليف</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(totalCosts)}</p>
              </div>
              <div className="text-3xl">💸</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">صافي الربح</p>
                <p className="text-2xl font-bold text-blue-600">{formatCurrency(totalProfit)}</p>
              </div>
              <div className="text-3xl">📊</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">هامش الربح</p>
                <p className="text-2xl font-bold text-purple-600">
                  {((totalProfit / totalRevenue) * 100).toFixed(1)}%
                </p>
              </div>
              <div className="text-3xl">📈</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Monthly Production & Revenue */}
        <Card>
          <CardHeader>
            <CardTitle>📊 الإنتاج والإيرادات الشهرية</CardTitle>
            <CardDescription>مقارنة الإنتاج والإيرادات خلال الأشهر الماضية</CardDescription>
          </CardHeader>
          <CardContent>
            {monthlyProduction.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-4xl mb-2">📊</div>
                <p className="text-muted-foreground">لا توجد بيانات إنتاج حتى الآن</p>
                <p className="text-sm text-muted-foreground">ستظهر الرسوم البيانية هنا عند توفر البيانات</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={monthlyProduction}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="revenue"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="الإيرادات"
                  />
                  <Area
                    type="monotone"
                    dataKey="cost"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="التكاليف"
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Product Categories Revenue */}
        <Card>
          <CardHeader>
            <CardTitle>🥧 إيرادات فئات المنتجات</CardTitle>
            <CardDescription>توزيع الإيرادات حسب فئة المنتج</CardDescription>
          </CardHeader>
          <CardContent>
            {productCategoryData.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-4xl mb-2">🥧</div>
                <p className="text-muted-foreground">لا توجد بيانات فئات المنتجات حتى الآن</p>
                <p className="text-sm text-muted-foreground">ستظهر الرسوم البيانية هنا عند إضافة المنتجات</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={productCategoryData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="revenue"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {productCategoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'الإيرادات']} />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Material Costs */}
        <Card>
          <CardHeader>
            <CardTitle>🧪 تكاليف المواد الخام</CardTitle>
            <CardDescription>توزيع تكاليف المواد الخام</CardDescription>
          </CardHeader>
          <CardContent>
            {materialCosts.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-4xl mb-2">🧪</div>
                <p className="text-muted-foreground">لا توجد بيانات تكاليف المواد الخام حتى الآن</p>
                <p className="text-sm text-muted-foreground">ستظهر الرسوم البيانية هنا عند إضافة المواد الخام</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={materialCosts}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="material" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'التكلفة']} />
                  <Bar dataKey="cost" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Profit Margin Trend */}
        <Card>
          <CardHeader>
            <CardTitle>📈 اتجاه هامش الربح</CardTitle>
            <CardDescription>تطور هامش الربح عبر الأشهر</CardDescription>
          </CardHeader>
          <CardContent>
            {profitMarginData.length === 0 ? (
              <div className="text-center py-16">
                <div className="text-4xl mb-2">📈</div>
                <p className="text-muted-foreground">لا توجد بيانات هامش الربح حتى الآن</p>
                <p className="text-sm text-muted-foreground">ستظهر الرسوم البيانية هنا عند توفر البيانات المالية</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={profitMarginData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}%`, 'هامش الربح']} />
                  <Line
                    type="monotone"
                    dataKey="margin"
                    stroke="#8884d8"
                    strokeWidth={3}
                    dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Inventory Status */}
      <Card>
        <CardHeader>
          <CardTitle>📦 حالة المخزون</CardTitle>
          <CardDescription>المنتجات التي تحتاج إلى إعادة تخزين</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {inventoryStatus.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">📦</div>
                <p className="text-muted-foreground">لا توجد بيانات مخزون حتى الآن</p>
                <p className="text-sm text-muted-foreground">ستظهر حالة المخزون هنا عند إضافة المنتجات</p>
              </div>
            ) : (
              inventoryStatus.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      item.status === 'good' ? 'bg-green-500' :
                      item.status === 'low' ? 'bg-yellow-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <p className="font-medium">{item.product}</p>
                      <p className="text-sm text-muted-foreground">
                        الحد الأدنى: {item.minimum} وحدة
                      </p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className={`font-bold ${
                      item.status === 'good' ? 'text-green-600' :
                      item.status === 'low' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {item.current} وحدة
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {item.status === 'good' ? '✅ مناسب' :
                       item.status === 'low' ? '⚠️ منخفض' : '🚨 حرج'}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;
