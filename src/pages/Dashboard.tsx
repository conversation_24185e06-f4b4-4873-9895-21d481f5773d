
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Dashboard() {
  // بيانات تجريبية للإحصائيات
  const stats = [
    {
      title: "إجمالي المنتجات",
      value: "24",
      description: "منتج مختلف",
      icon: "🧴",
      color: "bg-blue-500"
    },
    {
      title: "إجمالي الخامات",
      value: "18",
      description: "نوع خامة",
      icon: "🧪",
      color: "bg-green-500"
    },
    {
      title: "الإنتاج اليومي",
      value: "150",
      description: "وحدة منتجة",
      icon: "📊",
      color: "bg-orange-500"
    },
    {
      title: "المخزون",
      value: "89%",
      description: "من السعة الكاملة",
      icon: "📦",
      color: "bg-purple-500"
    }
  ];

  const recentActivities = [
    { action: "إضافة منتج جديد", item: "كريم مرطب للوجه", time: "منذ ساعتين" },
    { action: "تحديث مخزون", item: "زيت الأرجان", time: "منذ 4 ساعات" },
    { action: "إنتاج دفعة جديدة", item: "شاور جل الليمون", time: "منذ 6 ساعات" },
    { action: "إضافة خامة", item: "فيتامين E", time: "أمس" }
  ];

  return (
    <div className="space-y-6">
      {/* العنوان الرئيسي */}
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">لوحة التحكم</h1>
        <p className="text-muted-foreground">نظرة عامة على حالة المصنع والإنتاج</p>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={`w-10 h-10 rounded-full ${stat.color} flex items-center justify-center text-white text-xl`}>
                {stat.icon}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* الأنشطة الأخيرة */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📋 الأنشطة الأخيرة
            </CardTitle>
            <CardDescription>
              آخر العمليات في النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
                  <div>
                    <p className="font-medium text-foreground">{activity.action}</p>
                    <p className="text-sm text-muted-foreground">{activity.item}</p>
                  </div>
                  <span className="text-xs text-muted-foreground">{activity.time}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* ملخص الإنتاج */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📈 ملخص الإنتاج
            </CardTitle>
            <CardDescription>
              الإنتاج خلال الأسبوع الماضي
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">كريمات الوجه</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-secondary rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                  <span className="text-sm text-muted-foreground">75%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">شاور جل</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-secondary rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                  <span className="text-sm text-muted-foreground">90%</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">لوشن الجسم</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-secondary rounded-full h-2">
                    <div className="bg-orange-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <span className="text-sm text-muted-foreground">60%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
