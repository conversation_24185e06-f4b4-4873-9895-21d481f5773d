import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
// تم إزالة Select المعطل
import { useToast } from "@/hooks/use-toast";
import { MATERIAL_TYPES, UNITS, formatCurrency, formatDate } from "@/lib/constants";
import { materialService, Material } from "@/lib/firestore";

const FirestoreMaterials = () => {
  const { toast } = useToast();
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);

  const [newMaterial, setNewMaterial] = useState<Omit<Material, 'id' | 'createdAt' | 'updatedAt'>>({
    name: '',
    type: '',
    quantity: 0,
    unit: '',
    costPerUnit: 0,
    supplier: '',
    expiryDate: ''
  });

  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const materialTypes = MATERIAL_TYPES;
  const units = UNITS;

  // جلب المواد الخام من Firestore
  useEffect(() => {
    const loadMaterials = async () => {
      try {
        setLoading(true);
        const materialsData = await materialService.getMaterials();
        setMaterials(materialsData);
      } catch (error) {
        toast({
          title: "خطأ في جلب المواد الخام",
          description: "حدث خطأ أثناء جلب البيانات من قاعدة البيانات",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadMaterials();

    // الاستماع للتغييرات في الوقت الفعلي
    const unsubscribe = materialService.onMaterialsChange((updatedMaterials) => {
      setMaterials(updatedMaterials);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [toast]);

  const handleAddMaterial = async () => {
    if (newMaterial.name && newMaterial.type && newMaterial.unit) {
      try {
        await materialService.addMaterial(newMaterial);
        
        setNewMaterial({
          name: '',
          type: '',
          quantity: 0,
          unit: '',
          costPerUnit: 0,
          supplier: '',
          expiryDate: ''
        });
        setIsDialogOpen(false);
        
        toast({
          title: "تم إضافة المادة الخام",
          description: `تم إضافة ${newMaterial.name} بنجاح`
        });
      } catch (error) {
        toast({
          title: "خطأ في إضافة المادة الخام",
          description: "حدث خطأ أثناء حفظ المادة الخام",
          variant: "destructive"
        });
      }
    }
  };

  const handleEditMaterial = async () => {
    if (editingMaterial && editingMaterial.id) {
      try {
        await materialService.updateMaterial(editingMaterial.id, editingMaterial);
        
        setEditingMaterial(null);
        setIsDialogOpen(false);
        
        toast({
          title: "تم تحديث المادة الخام",
          description: `تم تحديث ${editingMaterial.name} بنجاح`
        });
      } catch (error) {
        toast({
          title: "خطأ في تحديث المادة الخام",
          description: "حدث خطأ أثناء تحديث المادة الخام",
          variant: "destructive"
        });
      }
    }
  };

  const handleDeleteMaterial = async (id: string) => {
    try {
      await materialService.deleteMaterial(id);
      toast({
        title: "تم حذف المادة الخام",
        description: "تم حذف المادة الخام بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ في حذف المادة الخام",
        description: "حدث خطأ أثناء حذف المادة الخام",
        variant: "destructive"
      });
    }
  };

  const openEditDialog = (material: Material) => {
    setEditingMaterial(material);
    setIsDialogOpen(true);
  };

  const resetForm = () => {
    setNewMaterial({
      name: '',
      type: '',
      quantity: 0,
      unit: '',
      costPerUnit: 0,
      supplier: '',
      expiryDate: ''
    });
    setEditingMaterial(null);
  };

  const getTotalValue = (material: Material) => {
    return (material.quantity * material.costPerUnit).toFixed(2);
  };

  const isExpiringSoon = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays >= 0;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">🧪 إدارة المواد الخام</h1>
          <p className="text-muted-foreground">جاري تحميل المواد الخام...</p>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">🧪 إدارة المواد الخام</h1>
          <p className="text-muted-foreground">إدارة مخزون المواد الخام والموردين</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>➕ إضافة مادة خام جديدة</Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingMaterial ? "تعديل المادة الخام" : "إضافة مادة خام جديدة"}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>اسم المادة الخام</Label>
                <Input
                  value={editingMaterial ? editingMaterial.name : newMaterial.name}
                  onChange={(e) => editingMaterial 
                    ? setEditingMaterial({...editingMaterial, name: e.target.value})
                    : setNewMaterial({...newMaterial, name: e.target.value})
                  }
                  placeholder="أدخل اسم المادة الخام"
                />
              </div>
              
              <div className="space-y-2">
                <Label>النوع</Label>
                <select
                  value={editingMaterial ? editingMaterial.type : newMaterial.type}
                  onChange={(e) => editingMaterial
                    ? setEditingMaterial({...editingMaterial, type: e.target.value})
                    : setNewMaterial({...newMaterial, type: e.target.value})
                  }
                  className="w-full p-3 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                >
                  <option value="">اختر النوع</option>
                  {materialTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>الكمية</Label>
                  <Input
                    type="number"
                    value={editingMaterial ? editingMaterial.quantity : newMaterial.quantity}
                    onChange={(e) => editingMaterial
                      ? setEditingMaterial({...editingMaterial, quantity: Number(e.target.value)})
                      : setNewMaterial({...newMaterial, quantity: Number(e.target.value)})
                    }
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>الوحدة</Label>
                  <select
                    value={editingMaterial ? editingMaterial.unit : newMaterial.unit}
                    onChange={(e) => editingMaterial
                      ? setEditingMaterial({...editingMaterial, unit: e.target.value})
                      : setNewMaterial({...newMaterial, unit: e.target.value})
                    }
                    className="w-full p-3 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                  >
                    <option value="">اختر الوحدة</option>
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>التكلفة لكل وحدة (ج.م)</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingMaterial ? editingMaterial.costPerUnit : newMaterial.costPerUnit}
                  onChange={(e) => editingMaterial
                    ? setEditingMaterial({...editingMaterial, costPerUnit: Number(e.target.value)})
                    : setNewMaterial({...newMaterial, costPerUnit: Number(e.target.value)})
                  }
                />
              </div>
              
              <div className="space-y-2">
                <Label>المورد</Label>
                <Input
                  value={editingMaterial ? editingMaterial.supplier : newMaterial.supplier}
                  onChange={(e) => editingMaterial
                    ? setEditingMaterial({...editingMaterial, supplier: e.target.value})
                    : setNewMaterial({...newMaterial, supplier: e.target.value})
                  }
                  placeholder="اسم المورد"
                />
              </div>
              
              <div className="space-y-2">
                <Label>تاريخ الانتهاء</Label>
                <Input
                  type="date"
                  value={editingMaterial ? editingMaterial.expiryDate : newMaterial.expiryDate}
                  onChange={(e) => editingMaterial
                    ? setEditingMaterial({...editingMaterial, expiryDate: e.target.value})
                    : setNewMaterial({...newMaterial, expiryDate: e.target.value})
                  }
                />
              </div>
              
              <Button 
                onClick={editingMaterial ? handleEditMaterial : handleAddMaterial}
                className="w-full"
              >
                {editingMaterial ? "تحديث المادة الخام" : "إضافة المادة الخام"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {materials.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">🧪</div>
            <h3 className="text-lg font-medium mb-2">لا توجد مواد خام حتى الآن</h3>
            <p className="text-muted-foreground mb-4">ابدأ بإضافة مواد خام جديدة لمصنعك</p>
            <Button onClick={() => setIsDialogOpen(true)}>
              ➕ إضافة أول مادة خام
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {materials.map((material) => (
            <Card key={material.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{material.name}</CardTitle>
                    <div className="flex gap-2 mt-1">
                      <Badge variant="secondary">{material.type}</Badge>
                      {isExpiringSoon(material.expiryDate) && (
                        <Badge variant="destructive">ينتهي قريباً</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(material)}
                    >
                      ✏️ تعديل
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => material.id && handleDeleteMaterial(material.id)}
                    >
                      🗑️ حذف
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <span className="text-muted-foreground">الكمية:</span>
                    <span className="font-medium mr-2">{material.quantity} {material.unit}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">التكلفة/وحدة:</span>
                    <span className="font-medium mr-2">{formatCurrency(material.costPerUnit)}</span>
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <span className="text-muted-foreground">القيمة الإجمالية:</span>
                    <span className="font-medium mr-2 text-green-600">
                      {formatCurrency(parseFloat(getTotalValue(material)))}
                    </span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <p>
                    <span className="text-muted-foreground">المورد:</span> 
                    <span className="font-medium mr-2">{material.supplier}</span>
                  </p>
                  <p>
                    <span className="text-muted-foreground">تاريخ الانتهاء:</span> 
                    <span className={isExpiringSoon(material.expiryDate) ? 'text-red-600 font-medium' : ''}>
                      {formatDate(material.expiryDate)}
                    </span>
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FirestoreMaterials;
