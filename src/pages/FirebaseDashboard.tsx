import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/constants";
import { productService, materialService, Product, Material } from "@/lib/firestore";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const FirebaseDashboard = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);

  // جلب البيانات من Firebase
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [productsData, materialsData] = await Promise.all([
          productService.getProducts(),
          materialService.getMaterials()
        ]);
        
        setProducts(productsData);
        setMaterials(materialsData);
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // الاستماع للتغييرات في الوقت الفعلي
    const unsubscribeProducts = productService.onProductsChange(setProducts);
    const unsubscribeMaterials = materialService.onMaterialsChange(setMaterials);

    return () => {
      unsubscribeProducts();
      unsubscribeMaterials();
    };
  }, []);

  // حساب الإحصائيات
  const calculateStats = () => {
    const totalProducts = products.length;
    const totalMaterials = materials.length;
    
    const totalProductsValue = products.reduce((sum, product) => 
      sum + (product.quantity * product.price), 0
    );
    
    const totalMaterialsValue = materials.reduce((sum, material) => 
      sum + (material.quantity * material.costPerUnit), 0
    );
    
    const totalProductsCost = products.reduce((sum, product) => 
      sum + (product.quantity * product.cost), 0
    );
    
    const totalProfit = totalProductsValue - totalProductsCost;
    
    return {
      totalProducts,
      totalMaterials,
      totalProductsValue,
      totalMaterialsValue,
      totalProfit,
      totalInventoryValue: totalProductsValue + totalMaterialsValue
    };
  };

  const stats = calculateStats();

  // بيانات الرسوم البيانية
  const categoryData = products.reduce((acc, product) => {
    const existing = acc.find(item => item.category === product.category);
    if (existing) {
      existing.count += 1;
      existing.value += product.quantity * product.price;
    } else {
      acc.push({
        category: product.category,
        count: 1,
        value: product.quantity * product.price
      });
    }
    return acc;
  }, [] as Array<{category: string, count: number, value: number}>);

  const materialTypeData = materials.reduce((acc, material) => {
    const existing = acc.find(item => item.type === material.type);
    if (existing) {
      existing.count += 1;
      existing.value += material.quantity * material.costPerUnit;
    } else {
      acc.push({
        type: material.type,
        count: 1,
        value: material.quantity * material.costPerUnit
      });
    }
    return acc;
  }, [] as Array<{type: string, count: number, value: number}>);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">📊 لوحة التحكم</h1>
          <p className="text-muted-foreground">جاري تحميل البيانات...</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">📊 لوحة التحكم</h1>
        <p className="text-muted-foreground">نظرة عامة على أداء المصنع</p>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المنتجات</CardTitle>
            <span className="text-2xl">🧴</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              منتج مختلف
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المواد الخام</CardTitle>
            <span className="text-2xl">🧪</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMaterials}</div>
            <p className="text-xs text-muted-foreground">
              مادة خام مختلفة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيمة المنتجات</CardTitle>
            <span className="text-2xl">💰</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalProductsValue)}</div>
            <p className="text-xs text-muted-foreground">
              إجمالي قيمة المنتجات
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيمة المواد الخام</CardTitle>
            <span className="text-2xl">📦</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalMaterialsValue)}</div>
            <p className="text-xs text-muted-foreground">
              إجمالي قيمة المواد الخام
            </p>
          </CardContent>
        </Card>
      </div>

      {/* إحصائيات إضافية */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">💹 الملخص المالي</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">إجمالي قيمة المخزون:</span>
              <span className="font-bold">{formatCurrency(stats.totalInventoryValue)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">الربح المتوقع:</span>
              <span className="font-bold text-green-600">{formatCurrency(stats.totalProfit)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">هامش الربح:</span>
              <span className="font-bold">
                {stats.totalProductsValue > 0 
                  ? `${((stats.totalProfit / stats.totalProductsValue) * 100).toFixed(1)}%`
                  : '0%'
                }
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">⚠️ تنبيهات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {materials.filter(material => {
                const today = new Date();
                const expiry = new Date(material.expiryDate);
                const diffTime = expiry.getTime() - today.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return diffDays <= 30 && diffDays >= 0;
              }).length > 0 ? (
                <div className="text-amber-600">
                  ⚠️ {materials.filter(material => {
                    const today = new Date();
                    const expiry = new Date(material.expiryDate);
                    const diffTime = expiry.getTime() - today.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays <= 30 && diffDays >= 0;
                  }).length} مادة خام تنتهي خلال 30 يوم
                </div>
              ) : (
                <div className="text-green-600">
                  ✅ جميع المواد الخام صالحة
                </div>
              )}
              
              {products.filter(product => product.quantity < 10).length > 0 ? (
                <div className="text-amber-600">
                  ⚠️ {products.filter(product => product.quantity < 10).length} منتج بكمية قليلة
                </div>
              ) : (
                <div className="text-green-600">
                  ✅ جميع المنتجات متوفرة بكمية كافية
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* الرسوم البيانية */}
      {categoryData.length > 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>📊 توزيع فئات المنتجات</CardTitle>
              <CardDescription>حسب القيمة</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={categoryData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [formatCurrency(Number(value)), 'القيمة']}
                    labelFormatter={(label) => `الفئة: ${label}`}
                  />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {materialTypeData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>🧪 توزيع أنواع المواد الخام</CardTitle>
                <CardDescription>حسب القيمة</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={materialTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({type, percent}) => `${type} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {materialTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'القيمة']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* رسالة إذا لم توجد بيانات */}
      {products.length === 0 && materials.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium mb-2">لا توجد بيانات حتى الآن</h3>
            <p className="text-muted-foreground mb-4">
              ابدأ بإضافة منتجات ومواد خام لرؤية الإحصائيات
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FirebaseDashboard;
