
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useAuth } from '../components/AuthContext';
import { useToast } from "@/hooks/use-toast";

const Settings = () => {
  const { theme, setTheme, changePassword } = useAuth();
  const { toast } = useToast();
  
  const [passwordForm, setPasswordForm] = useState({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [companyInfo, setCompanyInfo] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    taxNumber: ''
  });

  const handlePasswordChange = () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "خطأ في كلمة المرور",
        description: "كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين",
        variant: "destructive"
      });
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      toast({
        title: "خطأ في كلمة المرور",
        description: "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
        variant: "destructive"
      });
      return;
    }

    const success = changePassword(passwordForm.oldPassword, passwordForm.newPassword);
    if (success) {
      toast({
        title: "تم تغيير كلمة المرور بنجاح",
        description: "تم تحديث كلمة المرور الخاصة بك"
      });
      setPasswordForm({ oldPassword: '', newPassword: '', confirmPassword: '' });
    } else {
      toast({
        title: "خطأ في كلمة المرور القديمة",
        description: "كلمة المرور القديمة غير صحيحة",
        variant: "destructive"
      });
    }
  };

  const handleCompanyInfoSave = () => {
    // Save company info to localStorage or backend
    localStorage.setItem('companyInfo', JSON.stringify(companyInfo));
    toast({
      title: "تم حفظ معلومات الشركة",
      description: "تم تحديث معلومات الشركة بنجاح"
    });
  };

  const resetToDefaults = () => {
    setTheme('light');
    toast({
      title: "تم استعادة الإعدادات الافتراضية",
      description: "تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية"
    });
  };

  const exportData = () => {
    toast({
      title: "تصدير البيانات",
      description: "سيتم تصدير البيانات قريباً..."
    });
  };

  const importData = () => {
    toast({
      title: "استيراد البيانات",
      description: "سيتم استيراد البيانات قريباً..."
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-primary">⚙️ الإعدادات</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Theme Settings */}
        <Card>
          <CardHeader>
            <CardTitle>🎨 إعدادات المظهر</CardTitle>
            <CardDescription>تخصيص مظهر التطبيق</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="dark-mode">الوضع الداكن</Label>
                <p className="text-sm text-muted-foreground">تفعيل الوضع الداكن للعينين</p>
              </div>
              <Switch
                id="dark-mode"
                checked={theme === 'dark'}
                onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
              />
            </div>
            
            <div className="space-y-2">
              <Label>اللغة</Label>
              <div className="p-2 border rounded-md bg-muted">
                <span className="text-sm">العربية (افتراضي)</span>
              </div>
              <p className="text-xs text-muted-foreground">التطبيق يعمل باللغة العربية فقط</p>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle>🔒 الأمان</CardTitle>
            <CardDescription>إدارة كلمة المرور والأمان</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="old-password">كلمة المرور الحالية</Label>
              <Input
                id="old-password"
                type="password"
                value={passwordForm.oldPassword}
                onChange={(e) => setPasswordForm({...passwordForm, oldPassword: e.target.value})}
                placeholder="أدخل كلمة المرور الحالية"
              />
            </div>
            <div>
              <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
              <Input
                id="new-password"
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                placeholder="أدخل كلمة المرور الجديدة"
              />
            </div>
            <div>
              <Label htmlFor="confirm-password">تأكيد كلمة المرور</Label>
              <Input
                id="confirm-password"
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                placeholder="أعد إدخال كلمة المرور الجديدة"
              />
            </div>
            <Button onClick={handlePasswordChange} className="w-full">
              🔐 تغيير كلمة المرور
            </Button>
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>🏢 معلومات الشركة</CardTitle>
            <CardDescription>تحديث معلومات الشركة الأساسية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="company-name">اسم الشركة</Label>
                <Input
                  id="company-name"
                  value={companyInfo.name}
                  onChange={(e) => setCompanyInfo({...companyInfo, name: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="company-phone">رقم الهاتف</Label>
                <Input
                  id="company-phone"
                  value={companyInfo.phone}
                  onChange={(e) => setCompanyInfo({...companyInfo, phone: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="company-email">البريد الإلكتروني</Label>
                <Input
                  id="company-email"
                  type="email"
                  value={companyInfo.email}
                  onChange={(e) => setCompanyInfo({...companyInfo, email: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="company-tax">الرقم الضريبي</Label>
                <Input
                  id="company-tax"
                  value={companyInfo.taxNumber}
                  onChange={(e) => setCompanyInfo({...companyInfo, taxNumber: e.target.value})}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="company-address">العنوان</Label>
              <Input
                id="company-address"
                value={companyInfo.address}
                onChange={(e) => setCompanyInfo({...companyInfo, address: e.target.value})}
              />
            </div>
            <Button onClick={handleCompanyInfoSave} className="w-full">
              💾 حفظ معلومات الشركة
            </Button>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader>
            <CardTitle>📂 إدارة البيانات</CardTitle>
            <CardDescription>نسخ احتياطي واستعادة البيانات</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={exportData} variant="outline" className="w-full">
              📤 تصدير البيانات
            </Button>
            <Button onClick={importData} variant="outline" className="w-full">
              📥 استيراد البيانات
            </Button>
            <div className="pt-4 border-t">
              <p className="text-sm text-muted-foreground mb-2">
                آخر نسخة احتياطية: {new Date().toLocaleDateString('en-US')}
              </p>
              <Button variant="secondary" size="sm">
                🔄 إنشاء نسخة احتياطية
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle>🔧 إعدادات النظام</CardTitle>
            <CardDescription>إعدادات متقدمة للنظام</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>الإشعارات</Label>
                <p className="text-sm text-muted-foreground">تفعيل إشعارات النظام</p>
              </div>
              <Switch defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>التحديث التلقائي</Label>
                <p className="text-sm text-muted-foreground">تحديث البيانات تلقائياً</p>
              </div>
              <Switch defaultChecked />
            </div>

            <div className="pt-4 border-t">
              <Button onClick={resetToDefaults} variant="outline" className="w-full">
                🔄 استعادة الإعدادات الافتراضية
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>ℹ️ معلومات النظام</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3 text-sm">
            <div>
              <p className="text-muted-foreground">إصدار النظام</p>
              <p className="font-medium">v1.0.0</p>
            </div>
            <div>
              <p className="text-muted-foreground">آخر تحديث</p>
              <p className="font-medium">{new Date().toLocaleDateString('en-US')}</p>
            </div>
            <div>
              <p className="text-muted-foreground">المطور</p>
              <p className="font-medium">Khaled Nasser</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;
