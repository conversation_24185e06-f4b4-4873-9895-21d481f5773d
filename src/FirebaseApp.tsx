import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./components/FirebaseAuthContext";
import { FirebaseLayout } from "./components/FirebaseLayout";
import PrivateRoute from "./components/PrivateRoute";
import FirebaseDashboard from "./pages/FirebaseDashboard";
import FirestoreProducts from "./pages/FirestoreProducts";
import FirestoreMaterials from "./pages/FirestoreMaterials";
import Reports from "./pages/Reports";
import SimpleSettings from "./pages/SimpleSettings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const FirebaseApp = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <FirebaseLayout>
            <Routes>
              <Route path="/" element={
                <PrivateRoute>
                  <FirebaseDashboard />
                </PrivateRoute>
              } />
              <Route path="/products" element={
                <PrivateRoute>
                  <FirestoreProducts />
                </PrivateRoute>
              } />
              <Route path="/materials" element={
                <PrivateRoute>
                  <FirestoreMaterials />
                </PrivateRoute>
              } />
              <Route path="/reports" element={
                <PrivateRoute>
                  <Reports />
                </PrivateRoute>
              } />
              <Route path="/settings" element={
                <PrivateRoute>
                  <SimpleSettings />
                </PrivateRoute>
              } />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </FirebaseLayout>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default FirebaseApp;
