# دليل المدير - نظام إدارة مصنع الكريمات

## 🚀 البدء السريع

### 1. إعداد Firebase (مرة واحدة فقط)

1. **إنشاء مشروع Firebase:**
   - اذهب إلى [Firebase Console](https://console.firebase.google.com/)
   - انقر "إضافة مشروع" وأدخل اسم المشروع
   - اتبع خطوات الإعداد

2. **تفعيل المصادقة:**
   - في Firebase Console، اذهب إلى "Authentication"
   - انقر "البدء" ثم "Sign-in method"
   - فعّل "Email/Password"

3. **إضافة تطبيق ويب:**
   - في نظرة عامة المشروع، انقر أيقونة الويب `</>`
   - أدخل اسم التطبيق وانسخ التكوين

4. **تحديث التكوين:**
   - افتح ملف `src/lib/firebase.ts`
   - استبدل القيم التجريبية بتكوين مشروعك

### 2. إنشاء حساب المدير

1. في Firebase Console، اذهب إلى "Authentication" > "Users"
2. انقر "إضافة مستخدم"
3. أدخل البريد الإلكتروني وكلمة المرور
4. انقر "إضافة مستخدم"

## 🔐 إدارة المستخدمين

### إضافة مستخدم جديد
1. اذهب إلى Firebase Console
2. Authentication > Users
3. انقر "إضافة مستخدم"
4. أدخل البيانات المطلوبة

### حذف مستخدم
1. في قائمة المستخدمين، انقر على المستخدم
2. انقر "حذف المستخدم"
3. أكد الحذف

### إعادة تعيين كلمة المرور
1. انقر على المستخدم في القائمة
2. انقر "إعادة تعيين كلمة المرور"
3. سيتم إرسال رابط إعادة التعيين للبريد الإلكتروني

## 📊 استخدام النظام

### لوحة التحكم
- نظرة عامة على الإحصائيات
- الأنشطة الأخيرة
- ملخص الإنتاج

### إدارة المنتجات
- إضافة منتجات جديدة
- تعديل المنتجات الموجودة
- تتبع التكاليف والأرباح
- ربط المنتجات بالمواد الخام

### إدارة المواد الخام
- إضافة مواد خام جديدة (بما في ذلك "علب فارغة")
- متابعة المخزون
- تتبع تواريخ الانتهاء
- إدارة الموردين

### التقارير
- رسوم بيانية تفاعلية
- تقارير مالية
- إحصائيات الإنتاج
- حالة المخزون

### الإعدادات
- تغيير معلومات الشركة
- تبديل الوضع الداكن/الفاتح
- معلومات النظام

## 🛡️ الأمان

- **لا تشارك بيانات Firebase:** احتفظ بمفاتيح API آمنة
- **استخدم كلمات مرور قوية:** للحسابات في Firebase
- **راجع المستخدمين دورياً:** احذف الحسابات غير المستخدمة
- **فعّل التحقق بخطوتين:** في حساب Google الخاص بك

## 🔧 استكشاف الأخطاء

### مشكلة في تسجيل الدخول
1. تأكد من صحة البريد الإلكتروني وكلمة المرور
2. تحقق من وجود المستخدم في Firebase Console
3. تأكد من تفعيل Email/Password authentication

### مشكلة في التكوين
1. تحقق من ملف `src/lib/firebase.ts`
2. تأكد من صحة جميع القيم
3. تأكد من تفعيل Authentication في Firebase

### مشاكل الشبكة
1. تحقق من الاتصال بالإنترنت
2. تأكد من عدم حجب Firebase في الشبكة
3. جرب من متصفح مختلف

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف `FIREBASE_SETUP.md` للتعليمات التفصيلية
2. تحقق من [وثائق Firebase](https://firebase.google.com/docs)
3. تواصل مع المطور: Khaled Nasser
