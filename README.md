# 🏭 نظام إدارة مصنع الكريمات

نظام إدارة متكامل لمصانع الكريمات ومستحضرات التجميل باللغة العربية والجنيه المصري.

## 🌟 المميزات

- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات مع تتبع التكاليف والأرباح
- **إدارة المواد الخام**: متابعة المخزون وتواريخ الانتهاء والموردين
- **التقارير والإحصائيات**: رسوم بيانية تفاعلية وتقارير مالية شاملة
- **لوحة التحكم**: نظرة عامة على حالة المصنع والإنتاج
- **نظام الأمان**: تسجيل دخول آمن وإدارة كلمات المرور
- **الإعدادات**: تخصيص معلومات الشركة والمظهر

## 🚀 كيفية التشغيل

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التشغيل

```bash
# 1. تثبيت التبعيات
npm install

# 2. تشغيل التطبيق في وضع التطوير
npm run dev

# 3. فتح المتصفح على
http://localhost:8080
```

### إعداد Firebase
يستخدم التطبيق Firebase للمصادقة الآمنة:

1. **إعداد سريع**: راجع `QUICK_FIREBASE_GUIDE.md` (5 دقائق)
2. **إعداد تفصيلي**: راجع `FIREBASE_SETUP.md`
3. **إنشاء حساب المدير** في Firebase Console
4. **تحديث التكوين** في `src/lib/firebase.ts`

**بيانات تسجيل الدخول المقترحة:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `خالد ناصر 612712`

## 🛠️ التقنيات المستخدمة

- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة
- **Vite** - أداة البناء والتطوير
- **Firebase** - المصادقة الآمنة
- **Tailwind CSS** - إطار عمل التصميم
- **shadcn/ui** - مكونات واجهة المستخدم
- **Recharts** - الرسوم البيانية
- **React Router** - التنقل بين الصفحات

## 💰 العملة والتنسيق

- **العملة**: الجنيه المصري (ج.م)
- **اللغة**: العربية فقط
- **التنسيق**: يمين إلى يسار (RTL)
- **تنسيق التاريخ**: التقويم الهجري/الميلادي

## 📁 هيكل المشروع

```
src/
├── components/          # المكونات المشتركة
├── pages/              # صفحات التطبيق
├── lib/                # المكتبات والثوابت
├── hooks/              # React Hooks
└── App.tsx             # المكون الرئيسي
```

## 🔧 البناء للإنتاج

```bash
# بناء التطبيق للإنتاج
npm run build

# معاينة البناء
npm run preview
```

## 🔐 الأمان والمصادقة

- يستخدم Firebase Authentication للمصادقة الآمنة
- فقط المستخدمين المضافين في Firebase Console يمكنهم الوصول
- لا يوجد تسجيل مستخدمين جدد من التطبيق
- المدير يتحكم في جميع الحسابات من Firebase Console
- أمان عالي المستوى مع تشفير البيانات

## 📝 ملاحظات مهمة

- جميع البيانات محفوظة محلياً في المتصفح (localStorage)
- لا توجد بيانات تجريبية - التطبيق يبدأ فارغاً
- يمكن تطوير التطبيق ليتصل بقاعدة بيانات حقيقية
- التطبيق متجاوب ويعمل على جميع الأجهزة
- يجب إعداد Firebase قبل الاستخدام (5 دقائق فقط)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
